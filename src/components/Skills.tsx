import React, { useState, useEffect, useRef } from 'react';
import type { Skill } from '../types/cv';

interface SkillsProps {
  skills: Skill[];
}

const Skills: React.FC<SkillsProps> = ({ skills }) => {
  const [activeFilter, setActiveFilter] = useState<string>('*');
  const [, setAnimatedSkills] = useState<Set<number>>(new Set());
  const skillsRef = useRef<HTMLDivElement>(null);

  const skillCategories = {
    '*': 'All',
    'webs': 'Entornos web',
    'frameworks': 'Frameworks y Librerías',
    'prog': 'Programación',
    'db': 'Bases de datos',
    'apis': 'APIS y test unitarios',
    'sistem': 'Sistemas',
    'ofimatica': 'Ofimática',
    'otro': 'Otros'
  };

  const filteredSkills = activeFilter === '*'
    ? skills
    : skills.filter(skill => skill.category === activeFilter);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const skillElements = entry.target.querySelectorAll('.progress-bar');
            skillElements.forEach((_, index) => {
              setTimeout(() => {
                setAnimatedSkills(prev => new Set([...prev, index]));
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    if (skillsRef.current) {
      observer.observe(skillsRef.current);
    }

    return () => observer.disconnect();
  }, [filteredSkills]);

  return (
    <section id="skills" className="skills section-bg">
      <div className="container">
        <div className="section-title">
          <h2 className="text-uppercase">Skills</h2>
          <p>
            Como funciones realizadas en otros proyectos destaca análisis de código. También cabe destacar gestión de equipo,
            inicialización a gestión de proyectos, FullStack Developer, con años de experiencia en Drupal, Angular, Java... <br />
            Trato directo con el cliente. Social, proactivo y profesional y con ganas de aprender y realizar nuevos proyectos.
          </p>
        </div>

        <div className="row" data-aos="fade-up">
          <div className="col-lg-12 d-flex justify-content-center">
            <ul id="skills-flters">
              {Object.entries(skillCategories).map(([key, label]) => (
                <li
                  key={key}
                  data-filter={key === '*' ? '*' : `.filter-${key}`}
                  className={activeFilter === key ? 'filter-active' : ''}
                  onClick={() => setActiveFilter(key)}
                >
                  {label}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="row skills-content" data-aos="fade-up" data-aos-delay="100" ref={skillsRef}>
          {filteredSkills.map((skill, index) => (
            <div
              key={index}
              className={`col-lg-4 col-md-6 progress filter-${skill.category}`}
            >
              <span className="skill">
                {skill.name} <i className="val">{skill.percentage}%</i>
              </span>
              <div className="progress-bar-wrap">
                <div
                  className="progress-bar"
                  role="progressbar"
                  aria-valuenow={skill.percentage}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  style={{ width: `${skill.percentage}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
