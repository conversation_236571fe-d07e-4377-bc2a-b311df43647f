{"version": 3, "sources": ["typed.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "Object", "defineProperty", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "protoProps", "staticProps", "prototype", "_initializerJs", "_htmlParserJs", "Typed", "elementId", "options", "initializer", "load", "begin", "pause", "status", "start", "stop", "typingComplete", "toggleBlinking", "onStop", "arrayPos", "typewrite", "curString", "curStr<PERSON>os", "backspace", "onStart", "reset", "onDestroy", "restart", "arguments", "undefined", "clearInterval", "timeout", "replaceText", "cursor", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "strPos", "curL<PERSON>", "insertCursor", "onReset", "_this", "onBegin", "shuffleStringsIfNeeded", "bindInputFocusEvents", "bindFocusEvents", "setTimeout", "currentElContent", "strings", "sequence", "startDelay", "_this2", "fadeOut", "el", "classList", "contains", "fadeOutClass", "remove", "humanize", "humanizer", "typeSpeed", "numChars", "setPauseStatus", "htmlParser", "typeHtmlChars", "pauseTime", "substr", "char<PERSON>t", "test", "skip", "exec", "parseInt", "temporaryPause", "onTypingPaused", "substring", "stringBeforeSkip", "stringSkipped", "stringAfterSkip", "doneTyping", "keepTyping", "onTypingResumed", "preStringTyped", "nextString", "_this3", "onStringTyped", "complete", "loop", "loopCount", "<PERSON><PERSON><PERSON><PERSON>", "_this4", "initFadeOut", "backSpeed", "backSpaceHtmlChars", "curStringAtPosition", "smartBackspace", "stopNum", "onLastStringBackspaced", "onComplete", "isTyping", "isBlinking", "cursorBlinking", "add", "speed", "Math", "round", "random", "shuffle", "sort", "_this5", "className", "fadeOutDelay", "str", "attr", "setAttribute", "isInput", "contentType", "innerHTML", "textContent", "_this6", "addEventListener", "e", "showCursor", "document", "createElement", "cursor<PERSON><PERSON>", "insertBefore", "nextS<PERSON>ling", "_interopRequireDefault", "obj", "__esModule", "default", "_extends", "assign", "source", "hasOwnProperty", "_defaultsJs", "_defaultsJs2", "Initializer", "self", "querySelector", "tagName", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "getAttribute", "isPaused", "map", "s", "trim", "stringsElement", "style", "display", "Array", "slice", "apply", "children", "<PERSON><PERSON><PERSON><PERSON>", "stringEl", "push", "getCurrentElContent", "autoInsertCss", "appendAnimationCss", "cssDataName", "css", "type", "innerCss", "body", "append<PERSON><PERSON><PERSON>", "defaults", "Infinity", "HTMLParser", "cur<PERSON><PERSON>", "endTag"], "mappings": ";;;;;;;;;CASA,SAA2CA,EAAMC,GAC1B,gBAAZC,UAA0C,gBAAXC,QACxCA,OAAOD,QAAUD,IACQ,kBAAXG,SAAyBA,OAAOC,IAC9CD,UAAWH,GACe,gBAAZC,SACdA,QAAe,MAAID,IAEnBD,EAAY,MAAIC,MACfK,KAAM,WACT,MAAgB,UAAUC,GAKhB,QAASC,GAAoBC,GAG5B,GAAGC,EAAiBD,GACnB,MAAOC,GAAiBD,GAAUP,OAGnC,IAAIC,GAASO,EAAiBD,IAC7BP,WACAS,GAAIF,EACJG,QAAQ,EAUT,OANAL,GAAQE,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOS,QAAS,EAGTT,EAAOD,QAvBf,GAAIQ,KAqCJ,OATAF,GAAoBM,EAAIP,EAGxBC,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,GAGjBR,EAAoB,KAK/B,SAAUL,EAAQD,EAASM,GAEhC,YAQA,SAASS,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAIC,GAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAI7hBmB,EAAiB9B,EAAoB,GAErC+B,EAAgB/B,EAAoB,GASpCgC,EAAQ,WACV,QAASA,GAAMC,EAAWC,GACxBzB,EAAgBX,KAAMkC,GAGtBF,EAAeK,YAAYC,KAAKtC,KAAMoC,EAASD,GAE/CnC,KAAKuC,QAkdP,MA1cArB,GAAagB,IACXN,IAAK,SACLX,MAAO,WACLjB,KAAKwC,MAAMC,OAASzC,KAAK0C,QAAU1C,KAAK2C,UAQ1Cf,IAAK,OACLX,MAAO,WACDjB,KAAK4C,gBACL5C,KAAKwC,MAAMC,SACfzC,KAAK6C,gBAAe,GACpB7C,KAAKwC,MAAMC,QAAS,EACpBzC,KAAKoC,QAAQU,OAAO9C,KAAK+C,SAAU/C,UAQrC4B,IAAK,QACLX,MAAO,WACDjB,KAAK4C,gBACJ5C,KAAKwC,MAAMC,SAChBzC,KAAKwC,MAAMC,QAAS,EAChBzC,KAAKwC,MAAMQ,UACbhD,KAAKgD,UAAUhD,KAAKwC,MAAMS,UAAWjD,KAAKwC,MAAMU,WAEhDlD,KAAKmD,UAAUnD,KAAKwC,MAAMS,UAAWjD,KAAKwC,MAAMU,WAElDlD,KAAKoC,QAAQgB,QAAQpD,KAAK+C,SAAU/C,UAQtC4B,IAAK,UACLX,MAAO,WACLjB,KAAKqD,OAAM,GACXrD,KAAKoC,QAAQkB,UAAUtD,SASzB4B,IAAK,QACLX,MAAO,WACL,GAAIsC,GAAUC,UAAUjC,QAAU,GAAsBkC,SAAjBD,UAAU,IAA0BA,UAAU,EAErFE,eAAc1D,KAAK2D,SACnB3D,KAAK4D,YAAY,IACb5D,KAAK6D,QAAU7D,KAAK6D,OAAOC,aAC7B9D,KAAK6D,OAAOC,WAAWC,YAAY/D,KAAK6D,QACxC7D,KAAK6D,OAAS,MAEhB7D,KAAKgE,OAAS,EACdhE,KAAK+C,SAAW,EAChB/C,KAAKiE,QAAU,EACXV,IACFvD,KAAKkE,eACLlE,KAAKoC,QAAQ+B,QAAQnE,MACrBA,KAAKuC,YASTX,IAAK,QACLX,MAAO,WACL,GAAImD,GAAQpE,IAEZA,MAAKoC,QAAQiC,QAAQrE,MACrBA,KAAK4C,gBAAiB,EACtB5C,KAAKsE,uBAAuBtE,MAC5BA,KAAKkE,eACDlE,KAAKuE,sBAAsBvE,KAAKwE,kBACpCxE,KAAK2D,QAAUc,WAAW,WAEnBL,EAAMM,kBAAsD,IAAlCN,EAAMM,iBAAiBnD,OAIpD6C,EAAMjB,UAAUiB,EAAMM,iBAAkBN,EAAMM,iBAAiBnD,QAH/D6C,EAAMpB,UAAUoB,EAAMO,QAAQP,EAAMQ,SAASR,EAAMrB,WAAYqB,EAAMJ,SAKtEhE,KAAK6E,eAUVjD,IAAK,YACLX,MAAO,SAAmBgC,EAAWC,GACnC,GAAI4B,GAAS9E,IAETA,MAAK+E,SAAW/E,KAAKgF,GAAGC,UAAUC,SAASlF,KAAKmF,gBAClDnF,KAAKgF,GAAGC,UAAUG,OAAOpF,KAAKmF,cAC1BnF,KAAK6D,QAAQ7D,KAAK6D,OAAOoB,UAAUG,OAAOpF,KAAKmF,cAGrD,IAAIE,GAAWrF,KAAKsF,UAAUtF,KAAKuF,WAC/BC,EAAW,CAEf,OAAIxF,MAAKwC,MAAMC,UAAW,MACxBzC,MAAKyF,eAAexC,EAAWC,GAAW,QAK5ClD,KAAK2D,QAAUc,WAAW,WAExBvB,EAAYjB,EAAcyD,WAAWC,cAAc1C,EAAWC,EAAW4B,EAEzE,IAAIc,GAAY,EACZC,EAAS5C,EAAU4C,OAAO3C,EAI9B,IAAyB,MAArB2C,EAAOC,OAAO,IACZ,SAASC,KAAKF,GAAS,CACzB,GAAIG,GAAO,CACXH,GAAS,MAAMI,KAAKJ,GAAQ,GAC5BG,GAAQH,EAAOtE,OACfqE,EAAYM,SAASL,GACrBf,EAAOqB,gBAAiB,EACxBrB,EAAO1C,QAAQgE,eAAetB,EAAO/B,SAAU+B,GAE/C7B,EAAYA,EAAUoD,UAAU,EAAGnD,GAAaD,EAAUoD,UAAUnD,EAAY8C,GAChFlB,EAAOjC,gBAAe,GAM1B,GAAyB,MAArBgD,EAAOC,OAAO,GAAY,CAC5B,KAA4D,MAArD7C,EAAU4C,OAAO3C,EAAYsC,GAAUM,OAAO,KACnDN,MACItC,EAAYsC,EAAWvC,EAAU1B,WAGvC,GAAI+E,GAAmBrD,EAAUoD,UAAU,EAAGnD,GAC1CqD,EAAgBtD,EAAUoD,UAAUC,EAAiB/E,OAAS,EAAG2B,EAAYsC,GAC7EgB,EAAkBvD,EAAUoD,UAAUnD,EAAYsC,EAAW,EACjEvC,GAAYqD,EAAmBC,EAAgBC,EAC/ChB,IAIFV,EAAOnB,QAAUc,WAAW,WAE1BK,EAAOjC,gBAAe,GAGlBK,GAAaD,EAAU1B,OACzBuD,EAAO2B,WAAWxD,EAAWC,GAE7B4B,EAAO4B,WAAWzD,EAAWC,EAAWsC,GAGtCV,EAAOqB,iBACTrB,EAAOqB,gBAAiB,EACxBrB,EAAO1C,QAAQuE,gBAAgB7B,EAAO/B,SAAU+B,KAEjDc,IAGFP,OAULzD,IAAK,aACLX,MAAO,SAAoBgC,EAAWC,EAAWsC,GAE7B,IAAdtC,IACFlD,KAAK6C,gBAAe,GACpB7C,KAAKoC,QAAQwE,eAAe5G,KAAK+C,SAAU/C,OAI7CkD,GAAasC,CACb,IAAIqB,GAAa5D,EAAU4C,OAAO,EAAG3C,EACrClD,MAAK4D,YAAYiD,GAEjB7G,KAAKgD,UAAUC,EAAWC,MAU5BtB,IAAK,aACLX,MAAO,SAAoBgC,EAAWC,GACpC,GAAI4D,GAAS9G,IAGbA,MAAKoC,QAAQ2E,cAAc/G,KAAK+C,SAAU/C,MAC1CA,KAAK6C,gBAAe,GAEhB7C,KAAK+C,WAAa/C,KAAK2E,QAAQpD,OAAS,IAE1CvB,KAAKgH,WAEDhH,KAAKiH,QAAS,GAASjH,KAAKiE,UAAYjE,KAAKkH,aAInDlH,KAAK2D,QAAUc,WAAW,WACxBqC,EAAO3D,UAAUF,EAAWC,IAC3BlD,KAAKmH,eAUVvF,IAAK,YACLX,MAAO,SAAmBgC,EAAWC,GACnC,GAAIkE,GAASpH,IAEb,IAAIA,KAAKwC,MAAMC,UAAW,EAExB,WADAzC,MAAKyF,eAAexC,EAAWC,GAAW,EAG5C,IAAIlD,KAAK+E,QAAS,MAAO/E,MAAKqH,aAE9BrH,MAAK6C,gBAAe,EACpB,IAAIwC,GAAWrF,KAAKsF,UAAUtF,KAAKsH,UAEnCtH,MAAK2D,QAAUc,WAAW,WACxBvB,EAAYjB,EAAcyD,WAAW6B,mBAAmBtE,EAAWC,EAAWkE,EAE9E,IAAII,GAAsBvE,EAAU4C,OAAO,EAAG3C,EAI9C,IAHAkE,EAAOxD,YAAY4D,GAGfJ,EAAOK,eAAgB,CAEzB,GAAIZ,GAAaO,EAAOzC,QAAQyC,EAAOrE,SAAW,EAC9C8D,IAAcW,IAAwBX,EAAWhB,OAAO,EAAG3C,GAC7DkE,EAAOM,QAAUxE,EAEjBkE,EAAOM,QAAU,EAMjBxE,EAAYkE,EAAOM,SAErBxE,IAEAkE,EAAOjE,UAAUF,EAAWC,IACnBA,GAAakE,EAAOM,UAG7BN,EAAOrE,WAEHqE,EAAOrE,WAAaqE,EAAOzC,QAAQpD,QACrC6F,EAAOrE,SAAW,EAClBqE,EAAOhF,QAAQuF,yBACfP,EAAO9C,yBACP8C,EAAO7E,SAEP6E,EAAOpE,UAAUoE,EAAOzC,QAAQyC,EAAOxC,SAASwC,EAAOrE,WAAYG,KAItEmC,MAQLzD,IAAK,WACLX,MAAO,WACLjB,KAAKoC,QAAQwF,WAAW5H,MACpBA,KAAKiH,KACPjH,KAAKiE,UAELjE,KAAK4C,gBAAiB,KAY1BhB,IAAK,iBACLX,MAAO,SAAwBgC,EAAWC,EAAW2E,GACnD7H,KAAKwC,MAAMQ,UAAY6E,EACvB7H,KAAKwC,MAAMS,UAAYA,EACvBjD,KAAKwC,MAAMU,UAAYA,KASzBtB,IAAK,iBACLX,MAAO,SAAwB6G,GACxB9H,KAAK6D,SAEN7D,KAAKwC,MAAMC,QACXzC,KAAK+H,iBAAmBD,IAC5B9H,KAAK+H,eAAiBD,EAClBA,EACF9H,KAAK6D,OAAOoB,UAAU+C,IAAI,uBAE1BhI,KAAK6D,OAAOoB,UAAUG,OAAO,4BAUjCxD,IAAK,YACLX,MAAO,SAAmBgH,GACxB,MAAOC,MAAKC,MAAMD,KAAKE,SAAWH,EAAQ,GAAKA,KAQjDrG,IAAK,yBACLX,MAAO,WACAjB,KAAKqI,UACVrI,KAAK4E,SAAW5E,KAAK4E,SAAS0D,KAAK,WACjC,MAAOJ,MAAKE,SAAW,SAS3BxG,IAAK,cACLX,MAAO,WACL,GAAIsH,GAASvI,IAIb,OAFAA,MAAKgF,GAAGwD,WAAa,IAAMxI,KAAKmF,aAC5BnF,KAAK6D,SAAQ7D,KAAK6D,OAAO2E,WAAa,IAAMxI,KAAKmF,cAC9CV,WAAW,WAChB8D,EAAOxF,WACPwF,EAAO3E,YAAY,IAGf2E,EAAO5D,QAAQpD,OAASgH,EAAOxF,SACjCwF,EAAOvF,UAAUuF,EAAO5D,QAAQ4D,EAAO3D,SAAS2D,EAAOxF,WAAY,IAEnEwF,EAAOvF,UAAUuF,EAAO5D,QAAQ,GAAI,GACpC4D,EAAOxF,SAAW,IAEnB/C,KAAKyI,iBAUV7G,IAAK,cACLX,MAAO,SAAqByH,GACtB1I,KAAK2I,KACP3I,KAAKgF,GAAG4D,aAAa5I,KAAK2I,KAAMD,GAE5B1I,KAAK6I,QACP7I,KAAKgF,GAAG/D,MAAQyH,EACc,SAArB1I,KAAK8I,YACd9I,KAAKgF,GAAG+D,UAAYL,EAEpB1I,KAAKgF,GAAGgE,YAAcN,KAW5B9G,IAAK,kBACLX,MAAO,WACL,GAAIgI,GAASjJ,IAERA,MAAK6I,UACV7I,KAAKgF,GAAGkE,iBAAiB,QAAS,SAAUC,GAC1CF,EAAOtG,SAET3C,KAAKgF,GAAGkE,iBAAiB,OAAQ,SAAUC,GACrCF,EAAOjE,GAAG/D,OAAoC,IAA3BgI,EAAOjE,GAAG/D,MAAMM,QAGvC0H,EAAOvG,cASXd,IAAK,eACLX,MAAO,WACAjB,KAAKoJ,aACNpJ,KAAK6D,SACT7D,KAAK6D,OAASwF,SAASC,cAAc,QACrCtJ,KAAK6D,OAAO2E,UAAY,eACxBxI,KAAK6D,OAAO+E,aAAa,eAAe,GACxC5I,KAAK6D,OAAOkF,UAAY/I,KAAKuJ,WAC7BvJ,KAAKgF,GAAGlB,YAAc9D,KAAKgF,GAAGlB,WAAW0F,aAAaxJ,KAAK6D,OAAQ7D,KAAKgF,GAAGyE,mBAIxEvH,IAGTtC,GAAQ,WAAasC,EACrBrC,EAAOD,QAAUA,EAAQ,YAIpB,SAAUC,EAAQD,EAASM,GAEhC,YAUA,SAASwJ,GAAuBC,GAAO,MAAOA,IAAOA,EAAIC,WAAaD,GAAQE,UAAWF,GAEzF,QAAShJ,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAVhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAI6I,GAAW/I,OAAOgJ,QAAU,SAAU3I,GAAU,IAAK,GAAIE,GAAI,EAAGA,EAAIkC,UAAUjC,OAAQD,IAAK,CAAE,GAAI0I,GAASxG,UAAUlC,EAAI,KAAK,GAAIM,KAAOoI,GAAcjJ,OAAOgB,UAAUkI,eAAe1J,KAAKyJ,EAAQpI,KAAQR,EAAOQ,GAAOoI,EAAOpI,IAAY,MAAOR,IAEnPF,EAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAM7hBqJ,EAAchK,EAAoB,GAElCiK,EAAeT,EAAuBQ,GAMtCE,EAAc,WAChB,QAASA,KACPzJ,EAAgBX,KAAMoK,GAsLxB,MAnLAlJ,GAAakJ,IACXxI,IAAK,OAULX,MAAO,SAAcoJ,EAAMjI,EAASD,GAiElC,GA/DyB,gBAAdA,GACTkI,EAAKrF,GAAKqE,SAASiB,cAAcnI,GAEjCkI,EAAKrF,GAAK7C,EAGZkI,EAAKjI,QAAU0H,KAAaK,EAAa,WAAY/H,GAGrDiI,EAAKxB,QAA4C,UAAlCwB,EAAKrF,GAAGuF,QAAQC,cAC/BH,EAAK1B,KAAO0B,EAAKjI,QAAQuG,KACzB0B,EAAK9F,qBAAuB8F,EAAKjI,QAAQmC,qBAGzC8F,EAAKjB,YAAaiB,EAAKxB,SAAkBwB,EAAKjI,QAAQgH,WAGtDiB,EAAKd,WAAac,EAAKjI,QAAQmH,WAG/Bc,EAAKtC,gBAAiB,EAGtBsC,EAAKI,UAAYJ,EAAK1B,KAAO0B,EAAKrF,GAAG0F,aAAaL,EAAK1B,MAAQ0B,EAAKrF,GAAGgE,YAGvEqB,EAAKvB,YAAcuB,EAAKjI,QAAQ0G,YAGhCuB,EAAK9E,UAAY8E,EAAKjI,QAAQmD,UAG9B8E,EAAKxF,WAAawF,EAAKjI,QAAQyC,WAG/BwF,EAAK/C,UAAY+C,EAAKjI,QAAQkF,UAG9B+C,EAAK5C,eAAiB4C,EAAKjI,QAAQqF,eAGnC4C,EAAKlD,UAAYkD,EAAKjI,QAAQ+E,UAG9BkD,EAAKtF,QAAUsF,EAAKjI,QAAQ2C,QAC5BsF,EAAKlF,aAAekF,EAAKjI,QAAQ+C,aACjCkF,EAAK5B,aAAe4B,EAAKjI,QAAQqG,aAGjC4B,EAAKM,UAAW,EAGhBN,EAAK1F,QAAU0F,EAAKjI,QAAQuC,QAAQiG,IAAI,SAAUC,GAChD,MAAOA,GAAEC,SAIgC,gBAAhCT,GAAKjI,QAAQ2I,eACtBV,EAAKU,eAAiB1B,SAASiB,cAAcD,EAAKjI,QAAQ2I,gBAE1DV,EAAKU,eAAiBV,EAAKjI,QAAQ2I,eAGjCV,EAAKU,eAAgB,CACvBV,EAAK1F,WACL0F,EAAKU,eAAeC,MAAMC,QAAU,MACpC,IAAItG,GAAUuG,MAAMnJ,UAAUoJ,MAAMC,MAAMf,EAAKU,eAAeM,UAC1DC,EAAgB3G,EAAQpD,MAE5B,IAAI+J,EACF,IAAK,GAAIhK,GAAI,EAAGA,EAAIgK,EAAehK,GAAK,EAAG,CACzC,GAAIiK,GAAW5G,EAAQrD,EACvB+I,GAAK1F,QAAQ6G,KAAKD,EAASxC,UAAU+B,SAM3CT,EAAKrG,OAAS,EAGdqG,EAAKtH,SAAW,EAGhBsH,EAAK3C,QAAU,EAGf2C,EAAKpD,KAAOoD,EAAKjI,QAAQ6E,KACzBoD,EAAKnD,UAAYmD,EAAKjI,QAAQ8E,UAC9BmD,EAAKpG,QAAU,EAGfoG,EAAKhC,QAAUgC,EAAKjI,QAAQiG,QAE5BgC,EAAKzF,YAELyF,EAAK7H,OACHC,QAAQ,EACRO,WAAW,EACXC,UAAW,GACXC,UAAW,GAIbmH,EAAKzH,gBAAiB,CAGtB,KAAK,GAAItB,KAAK+I,GAAK1F,QACjB0F,EAAKzF,SAAStD,GAAKA,CAIrB+I,GAAK3F,iBAAmB1E,KAAKyL,oBAAoBpB,GAEjDA,EAAKqB,cAAgBrB,EAAKjI,QAAQsJ,cAElC1L,KAAK2L,mBAAmBtB,MAG1BzI,IAAK,sBACLX,MAAO,SAA6BoJ,GAClC,GAAII,GAAY,EAUhB,OAREA,GADEJ,EAAK1B,KACK0B,EAAKrF,GAAG0F,aAAaL,EAAK1B,MAC7B0B,EAAKxB,QACFwB,EAAKrF,GAAG/D,MACU,SAArBoJ,EAAKvB,YACFuB,EAAKrF,GAAG+D,UAERsB,EAAKrF,GAAGgE,eAKxBpH,IAAK,qBACLX,MAAO,SAA4BoJ,GACjC,GAAIuB,GAAc,mBAClB,IAAKvB,EAAKqB,gBAGLrB,EAAKjB,YAAeiB,EAAKtF,WAG1BsE,SAASiB,cAAc,IAAMsB,EAAc,KAA/C,CAIA,GAAIC,GAAMxC,SAASC,cAAc,QACjCuC,GAAIC,KAAO,WACXD,EAAIjD,aAAagD,GAAa,EAE9B,IAAIG,GAAW,EACX1B,GAAKjB,aACP2C,GAAY,qgBAEV1B,EAAKtF,UACPgH,GAAY,6OAEK,IAAfF,EAAItK,SAGRsK,EAAI9C,UAAYgD,EAChB1C,SAAS2C,KAAKC,YAAYJ,SAIvBzB,IAGTxK,GAAQ,WAAawK,CACrB,IAAI/H,GAAc,GAAI+H,EACtBxK,GAAQyC,YAAcA,GAIjB,SAAUxC,EAAQD,GAQvB,YAEAmB,QAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAET,IAAIiL,IAKFvH,SAAU,kCAAmC,+BAAgC,gBAAiB,qBAC9FoG,eAAgB,KAKhBxF,UAAW,EAKXV,WAAY,EAKZyC,UAAW,EAKXG,gBAAgB,EAKhBY,SAAS,EAKTlB,UAAW,IAOXpC,SAAS,EACTI,aAAc,iBACdsD,aAAc,IAMdxB,MAAM,EACNC,UAAWiF,EAAAA,EAOX/C,YAAY,EACZG,WAAY,IACZmC,eAAe,EAMf/C,KAAM,KAKNpE,sBAAsB,EAKtBuE,YAAa,OAMbzE,QAAS,SAAiBgG,KAM1BzC,WAAY,SAAoByC,KAOhCzD,eAAgB,SAAwB7D,EAAUsH,KAOlDtD,cAAe,SAAuBhE,EAAUsH,KAMhD1C,uBAAwB,SAAgC0C,KAOxDjE,eAAgB,SAAwBrD,EAAUsH,KAOlD1D,gBAAiB,SAAyB5D,EAAUsH,KAMpDlG,QAAS,SAAiBkG,KAO1BvH,OAAQ,SAAgBC,EAAUsH,KAOlCjH,QAAS,SAAiBL,EAAUsH,KAMpC/G,UAAW,SAAmB+G,KAGhCzK,GAAQ,WAAasM,EACrBrM,EAAOD,QAAUA,EAAQ,YAIpB,SAAUC,EAAQD,GAOvB,YAQA,SAASe,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhHC,OAAOC,eAAepB,EAAS,cAC7BqB,OAAO,GAGT,IAAIC,GAAe,WAAe,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CAAE,GAAIE,GAAaH,EAAMC,EAAIE,GAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,SAAWF,KAAYA,EAAWG,UAAW,GAAMZ,OAAOC,eAAeI,EAAQI,EAAWI,IAAKJ,IAAiB,MAAO,UAAUX,EAAagB,EAAYC,GAAiJ,MAA9HD,IAAYV,EAAiBN,EAAYkB,UAAWF,GAAiBC,GAAaX,EAAiBN,EAAaiB,GAAqBjB,MAI7hBuL,EAAa,WACf,QAASA,KACPzL,EAAgBX,KAAMoM,GAoExB,MAjEAlL,GAAakL,IACXxK,IAAK,gBAWLX,MAAO,SAAuBgC,EAAWC,EAAWmH,GAClD,GAAyB,SAArBA,EAAKvB,YAAwB,MAAO5F,EACxC,IAAImJ,GAAUpJ,EAAU4C,OAAO3C,GAAW4C,OAAO,EACjD,IAAgB,MAAZuG,GAA+B,MAAZA,EAAiB,CACtC,GAAIC,GAAS,EAMb,KAJEA,EADc,MAAZD,EACO,IAEA,IAEJpJ,EAAU4C,OAAO3C,EAAY,GAAG4C,OAAO,KAAOwG,IACnDpJ,MACIA,EAAY,EAAID,EAAU1B,WAIhC2B,IAEF,MAAOA,MAYTtB,IAAK,qBACLX,MAAO,SAA4BgC,EAAWC,EAAWmH,GACvD,GAAyB,SAArBA,EAAKvB,YAAwB,MAAO5F,EACxC,IAAImJ,GAAUpJ,EAAU4C,OAAO3C,GAAW4C,OAAO,EACjD,IAAgB,MAAZuG,GAA+B,MAAZA,EAAiB,CACtC,GAAIC,GAAS,EAMb,KAJEA,EADc,MAAZD,EACO,IAEA,IAEJpJ,EAAU4C,OAAO3C,EAAY,GAAG4C,OAAO,KAAOwG,IACnDpJ,MACIA,EAAY,MAIlBA,IAEF,MAAOA,OAIJkJ,IAGTxM,GAAQ,WAAawM,CACrB,IAAI1G,GAAa,GAAI0G,EACrBxM,GAAQ8F,WAAaA", "file": "typed.min.js", "sourcesContent": ["/*!\n * \n *   typed.js - A JavaScript Typing Animation Library\n *   Author: <PERSON> <<EMAIL>>\n *   Version: v2.0.12\n *   Url: https://github.com/mattboldt/typed.js\n *   License(s): MIT\n * \n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Typed\"] = factory();\n\telse\n\t\troot[\"Typed\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar _initializerJs = __webpack_require__(1);\n\t\n\tvar _htmlParserJs = __webpack_require__(3);\n\t\n\t/**\n\t * Welcome to Typed.js!\n\t * @param {string} elementId HTML element ID _OR_ HTML element\n\t * @param {object} options options object\n\t * @returns {object} a new Typed object\n\t */\n\t\n\tvar Typed = (function () {\n\t  function Typed(elementId, options) {\n\t    _classCallCheck(this, Typed);\n\t\n\t    // Initialize it up\n\t    _initializerJs.initializer.load(this, options, elementId);\n\t    // All systems go!\n\t    this.begin();\n\t  }\n\t\n\t  /**\n\t   * Toggle start() and stop() of the Typed instance\n\t   * @public\n\t   */\n\t\n\t  _createClass(Typed, [{\n\t    key: 'toggle',\n\t    value: function toggle() {\n\t      this.pause.status ? this.start() : this.stop();\n\t    }\n\t\n\t    /**\n\t     * Stop typing / backspacing and enable cursor blinking\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'stop',\n\t    value: function stop() {\n\t      if (this.typingComplete) return;\n\t      if (this.pause.status) return;\n\t      this.toggleBlinking(true);\n\t      this.pause.status = true;\n\t      this.options.onStop(this.arrayPos, this);\n\t    }\n\t\n\t    /**\n\t     * Start typing / backspacing after being stopped\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'start',\n\t    value: function start() {\n\t      if (this.typingComplete) return;\n\t      if (!this.pause.status) return;\n\t      this.pause.status = false;\n\t      if (this.pause.typewrite) {\n\t        this.typewrite(this.pause.curString, this.pause.curStrPos);\n\t      } else {\n\t        this.backspace(this.pause.curString, this.pause.curStrPos);\n\t      }\n\t      this.options.onStart(this.arrayPos, this);\n\t    }\n\t\n\t    /**\n\t     * Destroy this instance of Typed\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'destroy',\n\t    value: function destroy() {\n\t      this.reset(false);\n\t      this.options.onDestroy(this);\n\t    }\n\t\n\t    /**\n\t     * Reset Typed and optionally restarts\n\t     * @param {boolean} restart\n\t     * @public\n\t     */\n\t  }, {\n\t    key: 'reset',\n\t    value: function reset() {\n\t      var restart = arguments.length <= 0 || arguments[0] === undefined ? true : arguments[0];\n\t\n\t      clearInterval(this.timeout);\n\t      this.replaceText('');\n\t      if (this.cursor && this.cursor.parentNode) {\n\t        this.cursor.parentNode.removeChild(this.cursor);\n\t        this.cursor = null;\n\t      }\n\t      this.strPos = 0;\n\t      this.arrayPos = 0;\n\t      this.curLoop = 0;\n\t      if (restart) {\n\t        this.insertCursor();\n\t        this.options.onReset(this);\n\t        this.begin();\n\t      }\n\t    }\n\t\n\t    /**\n\t     * Begins the typing animation\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'begin',\n\t    value: function begin() {\n\t      var _this = this;\n\t\n\t      this.options.onBegin(this);\n\t      this.typingComplete = false;\n\t      this.shuffleStringsIfNeeded(this);\n\t      this.insertCursor();\n\t      if (this.bindInputFocusEvents) this.bindFocusEvents();\n\t      this.timeout = setTimeout(function () {\n\t        // Check if there is some text in the element, if yes start by backspacing the default message\n\t        if (!_this.currentElContent || _this.currentElContent.length === 0) {\n\t          _this.typewrite(_this.strings[_this.sequence[_this.arrayPos]], _this.strPos);\n\t        } else {\n\t          // Start typing\n\t          _this.backspace(_this.currentElContent, _this.currentElContent.length);\n\t        }\n\t      }, this.startDelay);\n\t    }\n\t\n\t    /**\n\t     * Called for each character typed\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'typewrite',\n\t    value: function typewrite(curString, curStrPos) {\n\t      var _this2 = this;\n\t\n\t      if (this.fadeOut && this.el.classList.contains(this.fadeOutClass)) {\n\t        this.el.classList.remove(this.fadeOutClass);\n\t        if (this.cursor) this.cursor.classList.remove(this.fadeOutClass);\n\t      }\n\t\n\t      var humanize = this.humanizer(this.typeSpeed);\n\t      var numChars = 1;\n\t\n\t      if (this.pause.status === true) {\n\t        this.setPauseStatus(curString, curStrPos, true);\n\t        return;\n\t      }\n\t\n\t      // contain typing function in a timeout humanize'd delay\n\t      this.timeout = setTimeout(function () {\n\t        // skip over any HTML chars\n\t        curStrPos = _htmlParserJs.htmlParser.typeHtmlChars(curString, curStrPos, _this2);\n\t\n\t        var pauseTime = 0;\n\t        var substr = curString.substr(curStrPos);\n\t        // check for an escape character before a pause value\n\t        // format: \\^\\d+ .. eg: ^1000 .. should be able to print the ^ too using ^^\n\t        // single ^ are removed from string\n\t        if (substr.charAt(0) === '^') {\n\t          if (/^\\^\\d+/.test(substr)) {\n\t            var skip = 1; // skip at least 1\n\t            substr = /\\d+/.exec(substr)[0];\n\t            skip += substr.length;\n\t            pauseTime = parseInt(substr);\n\t            _this2.temporaryPause = true;\n\t            _this2.options.onTypingPaused(_this2.arrayPos, _this2);\n\t            // strip out the escape character and pause value so they're not printed\n\t            curString = curString.substring(0, curStrPos) + curString.substring(curStrPos + skip);\n\t            _this2.toggleBlinking(true);\n\t          }\n\t        }\n\t\n\t        // check for skip characters formatted as\n\t        // \"this is a `string to print NOW` ...\"\n\t        if (substr.charAt(0) === '`') {\n\t          while (curString.substr(curStrPos + numChars).charAt(0) !== '`') {\n\t            numChars++;\n\t            if (curStrPos + numChars > curString.length) break;\n\t          }\n\t          // strip out the escape characters and append all the string in between\n\t          var stringBeforeSkip = curString.substring(0, curStrPos);\n\t          var stringSkipped = curString.substring(stringBeforeSkip.length + 1, curStrPos + numChars);\n\t          var stringAfterSkip = curString.substring(curStrPos + numChars + 1);\n\t          curString = stringBeforeSkip + stringSkipped + stringAfterSkip;\n\t          numChars--;\n\t        }\n\t\n\t        // timeout for any pause after a character\n\t        _this2.timeout = setTimeout(function () {\n\t          // Accounts for blinking while paused\n\t          _this2.toggleBlinking(false);\n\t\n\t          // We're done with this sentence!\n\t          if (curStrPos >= curString.length) {\n\t            _this2.doneTyping(curString, curStrPos);\n\t          } else {\n\t            _this2.keepTyping(curString, curStrPos, numChars);\n\t          }\n\t          // end of character pause\n\t          if (_this2.temporaryPause) {\n\t            _this2.temporaryPause = false;\n\t            _this2.options.onTypingResumed(_this2.arrayPos, _this2);\n\t          }\n\t        }, pauseTime);\n\t\n\t        // humanized value for typing\n\t      }, humanize);\n\t    }\n\t\n\t    /**\n\t     * Continue to the next string & begin typing\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'keepTyping',\n\t    value: function keepTyping(curString, curStrPos, numChars) {\n\t      // call before functions if applicable\n\t      if (curStrPos === 0) {\n\t        this.toggleBlinking(false);\n\t        this.options.preStringTyped(this.arrayPos, this);\n\t      }\n\t      // start typing each new char into existing string\n\t      // curString: arg, this.el.html: original text inside element\n\t      curStrPos += numChars;\n\t      var nextString = curString.substr(0, curStrPos);\n\t      this.replaceText(nextString);\n\t      // loop the function\n\t      this.typewrite(curString, curStrPos);\n\t    }\n\t\n\t    /**\n\t     * We're done typing the current string\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'doneTyping',\n\t    value: function doneTyping(curString, curStrPos) {\n\t      var _this3 = this;\n\t\n\t      // fires callback function\n\t      this.options.onStringTyped(this.arrayPos, this);\n\t      this.toggleBlinking(true);\n\t      // is this the final string\n\t      if (this.arrayPos === this.strings.length - 1) {\n\t        // callback that occurs on the last typed string\n\t        this.complete();\n\t        // quit if we wont loop back\n\t        if (this.loop === false || this.curLoop === this.loopCount) {\n\t          return;\n\t        }\n\t      }\n\t      this.timeout = setTimeout(function () {\n\t        _this3.backspace(curString, curStrPos);\n\t      }, this.backDelay);\n\t    }\n\t\n\t    /**\n\t     * Backspaces 1 character at a time\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'backspace',\n\t    value: function backspace(curString, curStrPos) {\n\t      var _this4 = this;\n\t\n\t      if (this.pause.status === true) {\n\t        this.setPauseStatus(curString, curStrPos, false);\n\t        return;\n\t      }\n\t      if (this.fadeOut) return this.initFadeOut();\n\t\n\t      this.toggleBlinking(false);\n\t      var humanize = this.humanizer(this.backSpeed);\n\t\n\t      this.timeout = setTimeout(function () {\n\t        curStrPos = _htmlParserJs.htmlParser.backSpaceHtmlChars(curString, curStrPos, _this4);\n\t        // replace text with base text + typed characters\n\t        var curStringAtPosition = curString.substr(0, curStrPos);\n\t        _this4.replaceText(curStringAtPosition);\n\t\n\t        // if smartBack is enabled\n\t        if (_this4.smartBackspace) {\n\t          // the remaining part of the current string is equal of the same part of the new string\n\t          var nextString = _this4.strings[_this4.arrayPos + 1];\n\t          if (nextString && curStringAtPosition === nextString.substr(0, curStrPos)) {\n\t            _this4.stopNum = curStrPos;\n\t          } else {\n\t            _this4.stopNum = 0;\n\t          }\n\t        }\n\t\n\t        // if the number (id of character in current string) is\n\t        // less than the stop number, keep going\n\t        if (curStrPos > _this4.stopNum) {\n\t          // subtract characters one by one\n\t          curStrPos--;\n\t          // loop the function\n\t          _this4.backspace(curString, curStrPos);\n\t        } else if (curStrPos <= _this4.stopNum) {\n\t          // if the stop number has been reached, increase\n\t          // array position to next string\n\t          _this4.arrayPos++;\n\t          // When looping, begin at the beginning after backspace complete\n\t          if (_this4.arrayPos === _this4.strings.length) {\n\t            _this4.arrayPos = 0;\n\t            _this4.options.onLastStringBackspaced();\n\t            _this4.shuffleStringsIfNeeded();\n\t            _this4.begin();\n\t          } else {\n\t            _this4.typewrite(_this4.strings[_this4.sequence[_this4.arrayPos]], curStrPos);\n\t          }\n\t        }\n\t        // humanized value for typing\n\t      }, humanize);\n\t    }\n\t\n\t    /**\n\t     * Full animation is complete\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'complete',\n\t    value: function complete() {\n\t      this.options.onComplete(this);\n\t      if (this.loop) {\n\t        this.curLoop++;\n\t      } else {\n\t        this.typingComplete = true;\n\t      }\n\t    }\n\t\n\t    /**\n\t     * Has the typing been stopped\n\t     * @param {string} curString the current string in the strings array\n\t     * @param {number} curStrPos the current position in the curString\n\t     * @param {boolean} isTyping\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'setPauseStatus',\n\t    value: function setPauseStatus(curString, curStrPos, isTyping) {\n\t      this.pause.typewrite = isTyping;\n\t      this.pause.curString = curString;\n\t      this.pause.curStrPos = curStrPos;\n\t    }\n\t\n\t    /**\n\t     * Toggle the blinking cursor\n\t     * @param {boolean} isBlinking\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'toggleBlinking',\n\t    value: function toggleBlinking(isBlinking) {\n\t      if (!this.cursor) return;\n\t      // if in paused state, don't toggle blinking a 2nd time\n\t      if (this.pause.status) return;\n\t      if (this.cursorBlinking === isBlinking) return;\n\t      this.cursorBlinking = isBlinking;\n\t      if (isBlinking) {\n\t        this.cursor.classList.add('typed-cursor--blink');\n\t      } else {\n\t        this.cursor.classList.remove('typed-cursor--blink');\n\t      }\n\t    }\n\t\n\t    /**\n\t     * Speed in MS to type\n\t     * @param {number} speed\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'humanizer',\n\t    value: function humanizer(speed) {\n\t      return Math.round(Math.random() * speed / 2) + speed;\n\t    }\n\t\n\t    /**\n\t     * Shuffle the sequence of the strings array\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'shuffleStringsIfNeeded',\n\t    value: function shuffleStringsIfNeeded() {\n\t      if (!this.shuffle) return;\n\t      this.sequence = this.sequence.sort(function () {\n\t        return Math.random() - 0.5;\n\t      });\n\t    }\n\t\n\t    /**\n\t     * Adds a CSS class to fade out current string\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'initFadeOut',\n\t    value: function initFadeOut() {\n\t      var _this5 = this;\n\t\n\t      this.el.className += ' ' + this.fadeOutClass;\n\t      if (this.cursor) this.cursor.className += ' ' + this.fadeOutClass;\n\t      return setTimeout(function () {\n\t        _this5.arrayPos++;\n\t        _this5.replaceText('');\n\t\n\t        // Resets current string if end of loop reached\n\t        if (_this5.strings.length > _this5.arrayPos) {\n\t          _this5.typewrite(_this5.strings[_this5.sequence[_this5.arrayPos]], 0);\n\t        } else {\n\t          _this5.typewrite(_this5.strings[0], 0);\n\t          _this5.arrayPos = 0;\n\t        }\n\t      }, this.fadeOutDelay);\n\t    }\n\t\n\t    /**\n\t     * Replaces current text in the HTML element\n\t     * depending on element type\n\t     * @param {string} str\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'replaceText',\n\t    value: function replaceText(str) {\n\t      if (this.attr) {\n\t        this.el.setAttribute(this.attr, str);\n\t      } else {\n\t        if (this.isInput) {\n\t          this.el.value = str;\n\t        } else if (this.contentType === 'html') {\n\t          this.el.innerHTML = str;\n\t        } else {\n\t          this.el.textContent = str;\n\t        }\n\t      }\n\t    }\n\t\n\t    /**\n\t     * If using input elements, bind focus in order to\n\t     * start and stop the animation\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'bindFocusEvents',\n\t    value: function bindFocusEvents() {\n\t      var _this6 = this;\n\t\n\t      if (!this.isInput) return;\n\t      this.el.addEventListener('focus', function (e) {\n\t        _this6.stop();\n\t      });\n\t      this.el.addEventListener('blur', function (e) {\n\t        if (_this6.el.value && _this6.el.value.length !== 0) {\n\t          return;\n\t        }\n\t        _this6.start();\n\t      });\n\t    }\n\t\n\t    /**\n\t     * On init, insert the cursor element\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'insertCursor',\n\t    value: function insertCursor() {\n\t      if (!this.showCursor) return;\n\t      if (this.cursor) return;\n\t      this.cursor = document.createElement('span');\n\t      this.cursor.className = 'typed-cursor';\n\t      this.cursor.setAttribute('aria-hidden', true);\n\t      this.cursor.innerHTML = this.cursorChar;\n\t      this.el.parentNode && this.el.parentNode.insertBefore(this.cursor, this.el.nextSibling);\n\t    }\n\t  }]);\n\t\n\t  return Typed;\n\t})();\n\t\n\texports['default'] = Typed;\n\tmodule.exports = exports['default'];\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar _defaultsJs = __webpack_require__(2);\n\t\n\tvar _defaultsJs2 = _interopRequireDefault(_defaultsJs);\n\t\n\t/**\n\t * Initialize the Typed object\n\t */\n\t\n\tvar Initializer = (function () {\n\t  function Initializer() {\n\t    _classCallCheck(this, Initializer);\n\t  }\n\t\n\t  _createClass(Initializer, [{\n\t    key: 'load',\n\t\n\t    /**\n\t     * Load up defaults & options on the Typed instance\n\t     * @param {Typed} self instance of Typed\n\t     * @param {object} options options object\n\t     * @param {string} elementId HTML element ID _OR_ instance of HTML element\n\t     * @private\n\t     */\n\t\n\t    value: function load(self, options, elementId) {\n\t      // chosen element to manipulate text\n\t      if (typeof elementId === 'string') {\n\t        self.el = document.querySelector(elementId);\n\t      } else {\n\t        self.el = elementId;\n\t      }\n\t\n\t      self.options = _extends({}, _defaultsJs2['default'], options);\n\t\n\t      // attribute to type into\n\t      self.isInput = self.el.tagName.toLowerCase() === 'input';\n\t      self.attr = self.options.attr;\n\t      self.bindInputFocusEvents = self.options.bindInputFocusEvents;\n\t\n\t      // show cursor\n\t      self.showCursor = self.isInput ? false : self.options.showCursor;\n\t\n\t      // custom cursor\n\t      self.cursorChar = self.options.cursorChar;\n\t\n\t      // Is the cursor blinking\n\t      self.cursorBlinking = true;\n\t\n\t      // text content of element\n\t      self.elContent = self.attr ? self.el.getAttribute(self.attr) : self.el.textContent;\n\t\n\t      // html or plain text\n\t      self.contentType = self.options.contentType;\n\t\n\t      // typing speed\n\t      self.typeSpeed = self.options.typeSpeed;\n\t\n\t      // add a delay before typing starts\n\t      self.startDelay = self.options.startDelay;\n\t\n\t      // backspacing speed\n\t      self.backSpeed = self.options.backSpeed;\n\t\n\t      // only backspace what doesn't match the previous string\n\t      self.smartBackspace = self.options.smartBackspace;\n\t\n\t      // amount of time to wait before backspacing\n\t      self.backDelay = self.options.backDelay;\n\t\n\t      // Fade out instead of backspace\n\t      self.fadeOut = self.options.fadeOut;\n\t      self.fadeOutClass = self.options.fadeOutClass;\n\t      self.fadeOutDelay = self.options.fadeOutDelay;\n\t\n\t      // variable to check whether typing is currently paused\n\t      self.isPaused = false;\n\t\n\t      // input strings of text\n\t      self.strings = self.options.strings.map(function (s) {\n\t        return s.trim();\n\t      });\n\t\n\t      // div containing strings\n\t      if (typeof self.options.stringsElement === 'string') {\n\t        self.stringsElement = document.querySelector(self.options.stringsElement);\n\t      } else {\n\t        self.stringsElement = self.options.stringsElement;\n\t      }\n\t\n\t      if (self.stringsElement) {\n\t        self.strings = [];\n\t        self.stringsElement.style.display = 'none';\n\t        var strings = Array.prototype.slice.apply(self.stringsElement.children);\n\t        var stringsLength = strings.length;\n\t\n\t        if (stringsLength) {\n\t          for (var i = 0; i < stringsLength; i += 1) {\n\t            var stringEl = strings[i];\n\t            self.strings.push(stringEl.innerHTML.trim());\n\t          }\n\t        }\n\t      }\n\t\n\t      // character number position of current string\n\t      self.strPos = 0;\n\t\n\t      // current array position\n\t      self.arrayPos = 0;\n\t\n\t      // index of string to stop backspacing on\n\t      self.stopNum = 0;\n\t\n\t      // Looping logic\n\t      self.loop = self.options.loop;\n\t      self.loopCount = self.options.loopCount;\n\t      self.curLoop = 0;\n\t\n\t      // shuffle the strings\n\t      self.shuffle = self.options.shuffle;\n\t      // the order of strings\n\t      self.sequence = [];\n\t\n\t      self.pause = {\n\t        status: false,\n\t        typewrite: true,\n\t        curString: '',\n\t        curStrPos: 0\n\t      };\n\t\n\t      // When the typing is complete (when not looped)\n\t      self.typingComplete = false;\n\t\n\t      // Set the order in which the strings are typed\n\t      for (var i in self.strings) {\n\t        self.sequence[i] = i;\n\t      }\n\t\n\t      // If there is some text in the element\n\t      self.currentElContent = this.getCurrentElContent(self);\n\t\n\t      self.autoInsertCss = self.options.autoInsertCss;\n\t\n\t      this.appendAnimationCss(self);\n\t    }\n\t  }, {\n\t    key: 'getCurrentElContent',\n\t    value: function getCurrentElContent(self) {\n\t      var elContent = '';\n\t      if (self.attr) {\n\t        elContent = self.el.getAttribute(self.attr);\n\t      } else if (self.isInput) {\n\t        elContent = self.el.value;\n\t      } else if (self.contentType === 'html') {\n\t        elContent = self.el.innerHTML;\n\t      } else {\n\t        elContent = self.el.textContent;\n\t      }\n\t      return elContent;\n\t    }\n\t  }, {\n\t    key: 'appendAnimationCss',\n\t    value: function appendAnimationCss(self) {\n\t      var cssDataName = 'data-typed-js-css';\n\t      if (!self.autoInsertCss) {\n\t        return;\n\t      }\n\t      if (!self.showCursor && !self.fadeOut) {\n\t        return;\n\t      }\n\t      if (document.querySelector('[' + cssDataName + ']')) {\n\t        return;\n\t      }\n\t\n\t      var css = document.createElement('style');\n\t      css.type = 'text/css';\n\t      css.setAttribute(cssDataName, true);\n\t\n\t      var innerCss = '';\n\t      if (self.showCursor) {\n\t        innerCss += '\\n        .typed-cursor{\\n          opacity: 1;\\n        }\\n        .typed-cursor.typed-cursor--blink{\\n          animation: typedjsBlink 0.7s infinite;\\n          -webkit-animation: typedjsBlink 0.7s infinite;\\n                  animation: typedjsBlink 0.7s infinite;\\n        }\\n        @keyframes typedjsBlink{\\n          50% { opacity: 0.0; }\\n        }\\n        @-webkit-keyframes typedjsBlink{\\n          0% { opacity: 1; }\\n          50% { opacity: 0.0; }\\n          100% { opacity: 1; }\\n        }\\n      ';\n\t      }\n\t      if (self.fadeOut) {\n\t        innerCss += '\\n        .typed-fade-out{\\n          opacity: 0;\\n          transition: opacity .25s;\\n        }\\n        .typed-cursor.typed-cursor--blink.typed-fade-out{\\n          -webkit-animation: 0;\\n          animation: 0;\\n        }\\n      ';\n\t      }\n\t      if (css.length === 0) {\n\t        return;\n\t      }\n\t      css.innerHTML = innerCss;\n\t      document.body.appendChild(css);\n\t    }\n\t  }]);\n\t\n\t  return Initializer;\n\t})();\n\t\n\texports['default'] = Initializer;\n\tvar initializer = new Initializer();\n\texports.initializer = initializer;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Defaults & options\n\t * @returns {object} Typed defaults & options\n\t * @public\n\t */\n\t\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\tvar defaults = {\n\t  /**\n\t   * @property {array} strings strings to be typed\n\t   * @property {string} stringsElement ID of element containing string children\n\t   */\n\t  strings: ['These are the default values...', 'You know what you should do?', 'Use your own!', 'Have a great day!'],\n\t  stringsElement: null,\n\t\n\t  /**\n\t   * @property {number} typeSpeed type speed in milliseconds\n\t   */\n\t  typeSpeed: 0,\n\t\n\t  /**\n\t   * @property {number} startDelay time before typing starts in milliseconds\n\t   */\n\t  startDelay: 0,\n\t\n\t  /**\n\t   * @property {number} backSpeed backspacing speed in milliseconds\n\t   */\n\t  backSpeed: 0,\n\t\n\t  /**\n\t   * @property {boolean} smartBackspace only backspace what doesn't match the previous string\n\t   */\n\t  smartBackspace: true,\n\t\n\t  /**\n\t   * @property {boolean} shuffle shuffle the strings\n\t   */\n\t  shuffle: false,\n\t\n\t  /**\n\t   * @property {number} backDelay time before backspacing in milliseconds\n\t   */\n\t  backDelay: 700,\n\t\n\t  /**\n\t   * @property {boolean} fadeOut Fade out instead of backspace\n\t   * @property {string} fadeOutClass css class for fade animation\n\t   * @property {boolean} fadeOutDelay Fade out delay in milliseconds\n\t   */\n\t  fadeOut: false,\n\t  fadeOutClass: 'typed-fade-out',\n\t  fadeOutDelay: 500,\n\t\n\t  /**\n\t   * @property {boolean} loop loop strings\n\t   * @property {number} loopCount amount of loops\n\t   */\n\t  loop: false,\n\t  loopCount: Infinity,\n\t\n\t  /**\n\t   * @property {boolean} showCursor show cursor\n\t   * @property {string} cursorChar character for cursor\n\t   * @property {boolean} autoInsertCss insert CSS for cursor and fadeOut into HTML <head>\n\t   */\n\t  showCursor: true,\n\t  cursorChar: '|',\n\t  autoInsertCss: true,\n\t\n\t  /**\n\t   * @property {string} attr attribute for typing\n\t   * Ex: input placeholder, value, or just HTML text\n\t   */\n\t  attr: null,\n\t\n\t  /**\n\t   * @property {boolean} bindInputFocusEvents bind to focus and blur if el is text input\n\t   */\n\t  bindInputFocusEvents: false,\n\t\n\t  /**\n\t   * @property {string} contentType 'html' or 'null' for plaintext\n\t   */\n\t  contentType: 'html',\n\t\n\t  /**\n\t   * Before it begins typing\n\t   * @param {Typed} self\n\t   */\n\t  onBegin: function onBegin(self) {},\n\t\n\t  /**\n\t   * All typing is complete\n\t   * @param {Typed} self\n\t   */\n\t  onComplete: function onComplete(self) {},\n\t\n\t  /**\n\t   * Before each string is typed\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  preStringTyped: function preStringTyped(arrayPos, self) {},\n\t\n\t  /**\n\t   * After each string is typed\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStringTyped: function onStringTyped(arrayPos, self) {},\n\t\n\t  /**\n\t   * During looping, after last string is typed\n\t   * @param {Typed} self\n\t   */\n\t  onLastStringBackspaced: function onLastStringBackspaced(self) {},\n\t\n\t  /**\n\t   * Typing has been stopped\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onTypingPaused: function onTypingPaused(arrayPos, self) {},\n\t\n\t  /**\n\t   * Typing has been started after being stopped\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onTypingResumed: function onTypingResumed(arrayPos, self) {},\n\t\n\t  /**\n\t   * After reset\n\t   * @param {Typed} self\n\t   */\n\t  onReset: function onReset(self) {},\n\t\n\t  /**\n\t   * After stop\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStop: function onStop(arrayPos, self) {},\n\t\n\t  /**\n\t   * After start\n\t   * @param {number} arrayPos\n\t   * @param {Typed} self\n\t   */\n\t  onStart: function onStart(arrayPos, self) {},\n\t\n\t  /**\n\t   * After destroy\n\t   * @param {Typed} self\n\t   */\n\t  onDestroy: function onDestroy(self) {}\n\t};\n\t\n\texports['default'] = defaults;\n\tmodule.exports = exports['default'];\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * TODO: These methods can probably be combined somehow\n\t * Parse HTML tags & HTML Characters\n\t */\n\t\n\t'use strict';\n\t\n\tObject.defineProperty(exports, '__esModule', {\n\t  value: true\n\t});\n\t\n\tvar _createClass = (function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; })();\n\t\n\tfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }\n\t\n\tvar HTMLParser = (function () {\n\t  function HTMLParser() {\n\t    _classCallCheck(this, HTMLParser);\n\t  }\n\t\n\t  _createClass(HTMLParser, [{\n\t    key: 'typeHtmlChars',\n\t\n\t    /**\n\t     * Type HTML tags & HTML Characters\n\t     * @param {string} curString Current string\n\t     * @param {number} curStrPos Position in current string\n\t     * @param {Typed} self instance of Typed\n\t     * @returns {number} a new string position\n\t     * @private\n\t     */\n\t\n\t    value: function typeHtmlChars(curString, curStrPos, self) {\n\t      if (self.contentType !== 'html') return curStrPos;\n\t      var curChar = curString.substr(curStrPos).charAt(0);\n\t      if (curChar === '<' || curChar === '&') {\n\t        var endTag = '';\n\t        if (curChar === '<') {\n\t          endTag = '>';\n\t        } else {\n\t          endTag = ';';\n\t        }\n\t        while (curString.substr(curStrPos + 1).charAt(0) !== endTag) {\n\t          curStrPos++;\n\t          if (curStrPos + 1 > curString.length) {\n\t            break;\n\t          }\n\t        }\n\t        curStrPos++;\n\t      }\n\t      return curStrPos;\n\t    }\n\t\n\t    /**\n\t     * Backspace HTML tags and HTML Characters\n\t     * @param {string} curString Current string\n\t     * @param {number} curStrPos Position in current string\n\t     * @param {Typed} self instance of Typed\n\t     * @returns {number} a new string position\n\t     * @private\n\t     */\n\t  }, {\n\t    key: 'backSpaceHtmlChars',\n\t    value: function backSpaceHtmlChars(curString, curStrPos, self) {\n\t      if (self.contentType !== 'html') return curStrPos;\n\t      var curChar = curString.substr(curStrPos).charAt(0);\n\t      if (curChar === '>' || curChar === ';') {\n\t        var endTag = '';\n\t        if (curChar === '>') {\n\t          endTag = '<';\n\t        } else {\n\t          endTag = '&';\n\t        }\n\t        while (curString.substr(curStrPos - 1).charAt(0) !== endTag) {\n\t          curStrPos--;\n\t          if (curStrPos < 0) {\n\t            break;\n\t          }\n\t        }\n\t        curStrPos--;\n\t      }\n\t      return curStrPos;\n\t    }\n\t  }]);\n\t\n\t  return HTMLParser;\n\t})();\n\t\n\texports['default'] = HTMLParser;\n\tvar htmlParser = new HTMLParser();\n\texports.htmlParser = htmlParser;\n\n/***/ })\n/******/ ])\n});\n;"]}