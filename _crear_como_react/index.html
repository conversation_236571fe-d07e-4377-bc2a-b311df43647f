<!DOCTYPE html>
<html lang="es">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">

  <title>C<PERSON> <PERSON></title>
  <meta content="" name="description">
  <meta content="" name="keywords">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Raleway:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Template Main CSS File -->
  <link href="assets/css/style.css" rel="stylesheet">
</head>

<body>
  <!-- ======= Mobile nav toggle button ======= -->
  <i class="bi bi-list mobile-nav-toggle d-xl-none"></i>

  <!-- ======= Header ======= -->
  <header id="header">
    <div class="d-flex flex-column">

      <div class="profile">
        <img src="assets/img/logo3d.png" alt="" class="img-fluid rounded-circle">
        <h1 class="text-light"><a href="index.html">Iván Jähnke</a></h1>
        <div class="social-links mt-3 text-center">
          <a href="https://github.com/MsArk" target="_blank" class="github"><i class="bx bxl-github"></i></a>
          <a href="https://www.linkedin.com/in/ivan-j-206a82110/" target="_blank" class="linkedin"><i class="bx bxl-linkedin"></i></a>
        </div>
      </div>

      <nav id="navbar" class="nav-menu navbar">
        <ul>
          <li>
            <a href="#hero" class="nav-link scrollto active">
              <i class="bx bx-home"></i>
              <span>Home</span>
            </a>
          </li>
          <li>
            <a href="#about" class="nav-link scrollto">
              <i class="bx bx-user"></i>
              <span>Sobre mi</span>
            </a>
          </li>
          <li>
            <a href="#resume" class="nav-link scrollto">
              <i class="bx bx-file-blank"></i>
              <span>Experiencia</span>
            </a>
          </li>
          <li>
            <a href="#portfolio" class="nav-link scrollto">
              <i class="bx bx-book-content"></i>
              <span>Portfolio</span>
            </a>
          </li>
          <li>
            <a href="#services" class="nav-link scrollto">
              <i class="bx bx-server"></i>
              <span>Servicios</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </header>
  <!-- End Header -->

  <!-- ======= Hero Section ======= -->
  <section id="hero" class="d-flex flex-column justify-content-center align-items-center">
    <div class="hero-container" data-aos="fade-in">
      <h1>Iván Jähnke</h1>
      <p>Soy <span class="typed" data-typed-items="Freelance, Developer PHP, Frontend, Analyst"></span></p>
    </div>
  </section>
  <!-- End Hero -->

  <main id="main">
    <!-- ======= About Section ======= -->
    <section id="about" class="about">
      <div class="container">

        <div class="section-title">
          <h2 class="text-uppercase">Sobre mi</h2>
          <p>
            Soy un trabajador que está acostumbrado al trabajo bajo presión, cuento con amplia
            experiencia en el mundo de la programación, principalmente en el sector sanitario.
            Mi perfil está orientado a front-end aunque también puedo realizar tareas
            tanto back-end y como devops.<br>
            Adicionalmente, cuento con una gran capacidad de análisis de la información y
            experiencia en la gestión de equipos.
            Me considero una persona resolutiva, con buen ánimo y capaz de resolver
            problemas fácilmente.<br>
            Por ahora me encuentro trabajando como autónomo en el sector de los eventos,
            pero sin dejar de lado la programación, ya que sigo realizando páginas web.
          </p>
        </div>

        <div class="row d-flex align-items-center">
          <div class="col-lg-4" data-aos="fade-right">
            <img src="assets/img/profile-img.jpg" class="img-fluid" alt="">
          </div>
          <div class="col-lg-8 pt-4 pt-lg-0 content" data-aos="fade-left">
            <h3>UI/UX Designer &amp; FullStack Developer.</h3>
            <div class="row">
              <div class="col-lg-6">
                <ul>
                  <!-- <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Website:</strong>
                    <span>
                      <a href="https://msarknet.com">https://msarknet.com</a>
                    </span>
                  </li> -->
                  <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Teléfono:</strong>
                    <span><a href="tel:+34605682559">+34 605 682 559</a></span>
                  </li>
                  <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Ciudad:</strong>
                    <span>Madrid, España</span>
                  </li>
                </ul>
              </div>

              <div class="col-lg-6">
                <ul>
                  <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Grado:</strong>
                    <span>Grado superior</span>
                  </li>
                  <!-- <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Email:</strong>
                    <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
                  </li> -->
                  <!-- <li>
                    <i class="bi bi-chevron-right"></i>
                    <strong>Freelance:</strong>
                    <span>En activo</span>
                  </li> -->
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End About Section -->

    <!-- ======= Skills Section ======= -->
    <section id="skills" class="skills section-bg">
      <div class="container">
        <div class="section-title">
          <h2 class="text-uppercase">Skills</h2>
          <p>
            Como funciones realizadas en otros proyectos destaca análisis de código. También cabe destacar gestión de equipo,
            inicialización a gestión de proyectos, FullStack Developer, con años de experiencia en Drupal, Angular, Java... <br>
            Trato directo con el cliente. Social, proactivo y profesional y con ganas de aprender y realizar nuevos proyectos.
          </p>
        </div>

        <div class="row" data-aos="fade-up">
          <div class="col-lg-12 d-flex justify-content-center">
            <ul id="skills-flters">
              <li data-filter="*" class="filter-active">All</li>
              <li data-filter=".filter-webs" class="filter-active">Entornos web</li>
              <li data-filter=".filter-frameworks">Frameworks y Librerías</li>
              <li data-filter=".filter-prog">Programación</li>
              <li data-filter=".filter-db">Bases de datos</li>
              <li data-filter=".filter-apis">APIS y test unitarios</li>
              <li data-filter=".filter-sistem">Sistemas</li>
              <li data-filter=".filter-ofimatica">Ofimática</li>
              <li data-filter=".filter-otro">Otros</li>
            </ul>
          </div>
        </div>

        <div class="row skills-content" data-aos="fade-up" data-aos-delay="100">
          <!-- Ofimática -->
          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Office <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Office 365 <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">XD <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Figma <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Mailchimp <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Acumbamail <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Zoom <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Teamviewer <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-ofimatica">
            <span class="skill">Chrome Desktop <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- Sistemas -->
          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">Powershell <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">Bash <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">SO Windows <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">SO Kubuntu / Ubuntu <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">SO WS 2008 / 2010 <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-sistem">
            <span class="skill">SO Ubuntu Server / Debian <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- APIS y Test -->
          <div class="col-lg-4 col-md-6 progress filter-apis">
            <span class="skill">Selenium <i class="val">80%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-apis">
            <span class="skill">Behat <i class="val">80%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-apis">
            <span class="skill">Postman <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-apis">
            <span class="skill">RESTful <i class="val">70%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-apis">
            <span class="skill">FastApi <i class="val">70%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- Otros -->
          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Metodología - Scrum <i class="val">80%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Control versiones - Git <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Control dependencias - Composer <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Control dependencias - NPM <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">CI / CD - Ansible <i class="val">25%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">CI / CD - Jenkins <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">CI / CD - Docker <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">CI / CD - Kubernetes <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Kanban <i class="val">80%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">Asana / Trello <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-otro">
            <span class="skill">OpenAPI / Swagger <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- Programación -->
          <div class="col-lg-4 col-md-6 progress filter-prog">
            <span class="skill">Java <i class="val">60%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-prog">
            <span class="skill">PHP <i class="val">95%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-prog">
            <span class="skill">Python <i class="val">40%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-prog">
            <span class="skill">Javascript <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-prog">
            <span class="skill">TypeScript <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- Frameworks -->
          <div class="col-lg-4 col-md-6 progress filter-frameworks">
            <span class="skill">AngularJS <i class="val">60%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-frameworks">
            <span class="skill">Angular <i class="val">70%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-frameworks">
            <span class="skill">React.js <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-frameworks">
            <span class="skill">VUE <i class="val">60%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-frameworks">
            <span class="skill">Bootstrap <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- Webs -->
          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">HTML5, XHTML <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">CSS3 <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">XML, XSD, XSL <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">JQuery <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">SCSS / SASS <i class="val">100%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">Drupal <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-webs">
            <span class="skill">Django <i class="val">25%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <!-- DB -->
          <div class="col-lg-4 col-md-6 progress filter-db">
            <span class="skill">MySQL <i class="val">90%</i></span>
            <div class="progress-MySQL-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-db">
            <span class="skill">MariaDB <i class="val">90%</i></span>
            <div class="progress-MySQL-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-db">
            <span class="skill">SQLite <i class="val">90%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-db">
            <span class="skill">Mongo <i class="val">20%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 progress filter-db">
            <span class="skill">Firebase <i class="val">80%</i></span>
            <div class="progress-bar-wrap">
              <div class="progress-bar" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End Skills Section -->

    <!-- ======= Resume Section ======= -->
    <section id="resume" class="resume">
      <div class="container">
        <div class="section-title">
          <h2 class="text-uppercase">Educación</h2>
        </div>

        <div class="row">
          <div class="col-lg-6" data-aos="fade-up">
            <h3 class="resume-title">Formación</h3>

            <div class="resume-item">
              <h4>Desarrollo de Aplicaciones</h4>
              <h5>2013 - 2015</h5>
              <p><em>IES Tetuán de las Victorias, Madrid, España</em></p>
            </div>

            <div class="resume-item">
              <h4>Sistemas Microinformáticos y Redes</h4>
              <h5>2011 - 2013</h5>
              <p><em>IES Tetuán de las Victorias, Madrid, España</em></p>
            </div>
          </div>

          <div class="col-lg-6" data-aos="fade-up">
            <h3 class="resume-title">Complementaria</h3>

            <div class="resume-item">
              <h4>Desarrollo de Aplicaciones</h4>
              <h5>2015</h5>
              <p><em>IES Tetuán de las Victorias, Madrid, España</em></p>
            </div>

            <div class="resume-item">
              <h4>Sistemas Microinformáticos y Redes</h4>
              <h5>2011 - 2013</h5>
              <p><em>IES Tetuán de las Victorias, Madrid, España</em></p>
            </div>
          </div>
        </div>
      </div>

      <div class="container mt-5">
        <div class="section-title">
          <h2 class="text-uppercase">Experiencia profesional</h2>
        </div>

        <div class="row">
          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <div class="resume-item">
              <h4>Full Stack Developer en Prototypes</h4>
              <h5>jul. 2023 - actualidad · 2 año 3 meses</h5>
              <p><em>Aszendit Tech, Madrid, España</em></p>
              <ul>
                <li>Desarrollo de aplicaciones web con arquitecturas modulares, escalables y mantenibles.</li>
                <li>Construcción de Agentes de IA y LLM</li>
                <li>UX/UI y desarollo de webs en la plataforma AURA</li>
                <li>Manejo de Cloud Azure</li>
                <li>Integración de la voz cognitiva de Azure en aplicaciones web</li>
                <li>Conversaciones fluidas humano e IA</li>
                <li>Manejo de firebase</li>
                <li>Métricas en GA4 + Looker Studio</li>
                <li>Creación de biblioteca de componentes en Storybook</li>
              </ul>
            </div>

            <div class="resume-item">
              <h4>Freelance</h4>
              <h5>jul. 2021 - jul. 2024  · 3 años</h5>
              <ul>
                <li>Desarrollo de páginas web</li>
                <li>Diseño</li>
                <li>Marketing SEO</li>
              </ul>
            </div>
          </div>

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <div class="resume-item">
              <h4>Headleader, Analista, Full Stack Developer</h4>
              <h5>sept. 2022 - jun. 2023  · 10 meses</h5>
              <p><em>IMAGINADS, Madrid, España</em></p>
              <ul>
                <li>Scrum master</li>
                <li>Desarrollo FE en angular</li>
                <li>Desarollo BE en python</li>
                <li>Desarollo de infraestructura de servidores. Montaje de maquinas virtuales y servidores web</li>
                <li>Conexión sockets para envio de videos hacia proyectores</li>
              </ul>
            </div>

            <div class="resume-item">
              <h4>Técnico en Audiovisuales - Freelance</h4>
              <h5>jul. 2021 - ago. 2022 · 1 año 2 meses</h5>
              <p><em>Kingston Audiovisuales, Madrid, España</em></p>
              <ul>
                <li>Realización de eventos</li>
                <li>Montaje de eventos</li>
              </ul>
            </div>
          </div>

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <div class="resume-item">
              <h4>Developer Full Stack</h4>
              <h5>abr. 2015 - jun. 2021 · 6 años 3 meses</h5>
              <p><em>Navandu Technologies, Madrid, España</em></p>
              <ul>
                <li>CMS en drupal</li>
              </ul>
            </div>

            <div class="resume-item">
              <h4>Help Desk</h4>
              <h5>abr. 2013 - jun. 2013 · 3 meses</h5>
              <p><em>Expacom Sistemas SI, Madrid, España</em></p>
              <ul>
                <li>Administración de redes locales</li>
                <li>Montaje de sistemas operativos</li>
                <li>Configuración teléfonos CISCO</li>
                <li>Montaje Fireware XTM Watchguard</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End Resume Section -->

    <!-- ======= Portfolio Section ======= -->
    <section id="portfolio" class="portfolio section-bg">
      <div class="container">
        <div class="section-title">
          <h2 class="text-uppercase">Portfolio</h2>
          <p>Magnam dolores commodi suscipit. Necessitatibus eius consequatur ex aliquid fuga eum quidem. Sit sint consectetur velit. Quisquam quos quisquam cupiditate. Et nemo qui impedit suscipit alias ea. Quia fugiat sit in iste officiis commodi quidem hic quas.</p>
        </div>

        <div class="row" data-aos="fade-up">
          <div class="col-lg-12 d-flex justify-content-center">
            <ul id="portfolio-flters">
              <li data-filter="*" class="filter-active">All</li>
              <li data-filter=".filter-app">App</li>
              <li data-filter=".filter-card">Card</li>
              <li data-filter=".filter-web">Web</li>
            </ul>
          </div>
        </div>

        <div class="row portfolio-container" data-aos="fade-up" data-aos-delay="100">
          <div class="col-lg-4 col-md-6 portfolio-item filter-app">
            <div class="portfolio-wrap">
              <img src="assets/img/portfolio/portfolio-1.jpg" class="img-fluid" alt="">
              <div class="portfolio-links">
                <a href="assets/img/portfolio/portfolio-1.jpg" data-gallery="portfolioGallery" class="portfolio-lightbox" title="App 1"><i class="bx bx-plus"></i></a>
                <a href="portfolio-details.html" title="More Details"><i class="bx bx-link"></i></a>
              </div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 portfolio-item filter-web">
            <div class="portfolio-wrap">
              <img src="assets/img/portfolio/portfolio-2.jpg" class="img-fluid" alt="">
              <div class="portfolio-links">
                <a href="assets/img/portfolio/portfolio-2.jpg" data-gallery="portfolioGallery" class="portfolio-lightbox" title="Web 3"><i class="bx bx-plus"></i></a>
                <a href="portfolio-details.html" title="More Details"><i class="bx bx-link"></i></a>
              </div>
            </div>
          </div>

          <div class="col-lg-4 col-md-6 portfolio-item filter-card">
            <div class="portfolio-wrap">
              <img src="assets/img/portfolio/portfolio-4.jpg" class="img-fluid" alt="">
              <div class="portfolio-links">
                <a href="assets/img/portfolio/portfolio-4.jpg" data-gallery="portfolioGallery" class="portfolio-lightbox" title="Card 2"><i class="bx bx-plus"></i></a>
                <a href="portfolio-details.html" title="More Details"><i class="bx bx-link"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End Portfolio Section -->

    <!-- ======= Services Section ======= -->
    <section id="services" class="services">
      <div class="container">
        <div class="section-title">
          <h2 class="text-uppercase">Services</h2>
          <p>Magnam dolores commodi suscipit. Necessitatibus eius consequatur ex aliquid fuga eum quidem. Sit sint consectetur velit. Quisquam quos quisquam cupiditate. Et nemo qui impedit suscipit alias ea. Quia fugiat sit in iste officiis commodi quidem hic quas.</p>
        </div>

        <div class="row">
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up">
            <div class="icon"><i class="bi bi-briefcase"></i></div>
            <h4 class="title"><a href="">Lorem Ipsum</a></h4>
            <p class="description">Voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident</p>
          </div>
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up" data-aos-delay="100">
            <div class="icon"><i class="bi bi-card-checklist"></i></div>
            <h4 class="title"><a href="">Dolor Sitema</a></h4>
            <p class="description">Minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat tarad limino ata</p>
          </div>
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up" data-aos-delay="200">
            <div class="icon"><i class="bi bi-bar-chart"></i></div>
            <h4 class="title"><a href="">Sed ut perspiciatis</a></h4>
            <p class="description">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur</p>
          </div>
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up" data-aos-delay="300">
            <div class="icon"><i class="bi bi-binoculars"></i></div>
            <h4 class="title"><a href="">Magni Dolores</a></h4>
            <p class="description">Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>
          </div>
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up" data-aos-delay="400">
            <div class="icon"><i class="bi bi-brightness-high"></i></div>
            <h4 class="title"><a href="">Nemo Enim</a></h4>
            <p class="description">At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque</p>
          </div>
          <div class="col-lg-4 col-md-6 icon-box" data-aos="fade-up" data-aos-delay="500">
            <div class="icon"><i class="bi bi-calendar4-week"></i></div>
            <h4 class="title"><a href="">Eiusmod Tempor</a></h4>
            <p class="description">Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi</p>
          </div>
        </div>
      </div>
    </section>
    <!-- End Services Section -->
  </main>
  <!-- End #main -->

  <!-- ======= Footer ======= -->
  <footer id="footer">
    <div class="container">
      <div class="copyright">
        &copy; Copyright <strong><span>Msarknet</span></strong>
      </div>
    </div>
  </footer>
  <!-- End  Footer -->

  <a href="#" class="back-to-top d-flex align-items-center justify-content-center">
    <i class="bi bi-arrow-up-short"></i>
  </a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="assets/vendor/typed.js/typed.min.js"></script>
  <script src="assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>
</body>
</html>
